// @ts-nocheck
import {  useEffect, useState, useRef } from "react";
import styles from "../createPo.module.scss";
import { Controller } from "react-hook-form";
import { Autocomplete, ClickAwayListener, Fade, Tooltip, ListSubheader } from "@mui/material";
import { CustomMenu } from "../CustomMenu";
import clsx from "clsx";
import { ReactComponent as RemoveLineIcon } from "../../../assets/images/Remove-line.svg";
import { ReactComponent as RemoveLineHoverIcon } from "../../../assets/images/Remove-line-hover.svg";
import { ReactComponent as QuestionIcon } from '../../../assets/images/setting-question.svg';
import { ReactComponent as QuestionHoverIcon } from '../../../assets/images/question-white-hover.svg';
import { ReactComponent as DomesticCheckTextIcon } from '../../../assets/New-images/Domestic-Check-Text.svg';
import { CommonTooltip } from "../../../component/Tooltip/tooltip";
import { v4 as uuidv4 } from 'uuid';
import { formatDollarPerUnit, searchProducts, getValidSearchData, useGlobalStore, MinSearchDataLen, formatToTwoDecimalPlaces, commomKeys, uploadBomConst, useCreatePoStore, getFormattedUnit, priceUnits } from '@bryzos/giss-ui-library';
import CustomTextField from "src/renderer2/component/CustomTextField";
import InputWrapper from "src/renderer2/component/InputWrapper";
import React from "react";
import LineStatus from "src/renderer2/component/LineStatus/LineStatus";
import usePostBomDraft from "src/renderer2/hooks/usePostBomDraft";
import useDialogStore from "src/renderer2/component/DialogPopup/DialogStore";
import { LargeProductsNameList, localStorageKeys } from "src/renderer2/common";

const SAME_TAG_ERROR_MESSAGE =
  "Same part? <br />You have applied this part number to another product already.";

// List of quantity units for dropdown
const QUANTITY_UNITS = [
  { title: "PC", value: priceUnits.pc },
  { title: "FT", value: priceUnits.ft },
  { title: "LB", value: priceUnits.lb },
  { title: "CWT", value: priceUnits.cwt }
];

// Helper function for case-insensitive unit comparison
const compareUnits = (unit1, unit2) => {
  if (!unit1 || !unit2) return false;
  return unit1.toLowerCase() === unit2.toLowerCase();
};

const BomTile = ({
  index,
  register,
  fields,
  products,
  control,
  setValue,
  watch,
  errors,
  getValues,
  userPartData,
  sessionId,
  selectedProduct,
  searchStringData,
  setSearchString,
  setLineSessionId,
  lineSessionId,
  handleCreatePOSearch,
  apiCallInProgress,
  updateLineProduct,
  removeLineItem,
  pricePerUnitChangeHandler,
  quantitySizeValidator,
  saveUserLineActivity,
  orderInfoIsFilled,
  setDisableBidBuyNow,
  openAddLineTab,
  setOpenDeliveryToDialog,
  hidePoLineScroll,
  setHidePoLineScroll,
  scrollToTop,
  bomTileDefaultData,
  viewIndex,
  clearErrors,
  lastModifiedBomRef,
  lastModifiedBom,
  currentBomData,
  viewLineStatusIndex,
  updateLineProductTag,
  filterFieldsLength,
  filteredItemIndex,
  validateSavedBomCreatePo
}) => {
  const { userData , referenceData, productMapping} = useGlobalStore();
  const { showCommonDialog, resetDialogStore } = useDialogStore();
  const [descriptionInput, setDescriptionInput] = useState("");
  const [sameTagErrorMsg, setSameTagErrorMsg] = useState(null);
  const [openDescription, setOpenDescription] = useState(false);
  const [isQtyInEditMode, setIsQtyInEditMode] = useState(false);
  const [showDomesticMaterialOnly, seShowDomesticMaterialOnly] = useState(true);
  const [enableRejectSearchAnalytic, setEnableRejectSearchAnalytic]= useState(false);
  const [focusPlaceHolder, setFocusPlaceHolder]= useState(false);
  const [hasSearchResults, setHasSearchResults] = useState(false);
  const [isDescriptionModeEnabled, setIsDescriptionModeEnabled] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const [isDomesticFocused, setIsDomesticFocused] = useState(false);
  const [initialOptions, setInitialOptions] = useState([]);
  const lineNumberRef = useRef(null);
  const [isHovered, setIsHovered] = useState(false);
  const [disableApproveButton, setDisableApproveButton] = useState(false);
  const [isMouseOverComponent, setIsMouseOverComponent] = useState(false);
  const {setBomDataIdToRefresh , setBomSummaryViewFilter , bomSummaryViewFilter} = useCreatePoStore();

  const isDescriptionObjPresent = watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0;
  const firstEmptyIndex = watch('cart_items') ? watch('cart_items').findIndex(item => !item?.descriptionObj || !item?.descriptionObj?.UI_Description) : -1;
  const hasDescriptionButNoMatches = !!(initialOptions.length === 0 && watch(`cart_items.${index}.description`));
  const { mutate: postBomDraft } = usePostBomDraft();
  const qtyRef = useRef(null);
  const productTagRef = useRef(null);
  // Custom Listbox component with header
  const ListboxComponent = React.forwardRef((props, ref) => {
    const { children, ...other } = props;
    // Get the count of child items (search results)
    const childCount = React.Children.count(children);
    return (
      <>
        {(hasDescriptionButNoMatches || (descriptionInput.length < 1)) &&
          <span className={'dropdownHeader'}>
          {hasDescriptionButNoMatches ?
           <span className={styles.matchesfoundSearch}>
           <span className={styles.textStyle1}>0</span>
           <span className={styles.textStyle2}> Matches </span>
           <span className={styles.textStyle3}> found.<br/> Search available products.</span>
         </span> :
            descriptionInput.length < 2 && 
            <span>Choose from <span className={'childCount'}>{childCount} </span><span className={'matches'}>{initialOptions.length === 1 ? 'Match' : 'Matches'}</span> below <br/> or use the search above.</span> }
        </span>
        }
      <ul ref={ref} {...other}>
        {children}
      </ul>
      </>
    );
  });
  
  // Custom component to show when no options match
  const NoOptionsComponent = React.forwardRef((props, ref) => {
    return (
      <div 
        ref={ref} 
        {...props}
        style={{
          padding: '10px 16px',
          color: '#666',
          textAlign: 'center',
          fontSize: '14px',
          fontWeight: '500',
          backgroundColor: '#f8f8f8',
          borderRadius: '4px',
          margin: '10px'
        }}
      >
        {descriptionInput.length > 1 
          ? "No matching products found" 
          : initialOptions.length > 0 
            ? "No suggestions available" 
            : "Type to search for products"}
      </div>
    );
  });

  const isOrderLineDisabled = !(isDescriptionObjPresent) && !orderInfoIsFilled;
  const hasSingleMatchedProduct = watch(`cart_items.${index}.matched_products`)?.length === 1;

  useEffect(() => {
    // setValue(`cart_items.${index}.line_session_id`, uuidv4());
    // if (index > 0) {
    //   saveUserActivity();
    // }
    qtyEditModeCloseHandler();
    //Remove this code
    //   setValue(`cart_items.${index}.lineStatus`, 'APPROVED');
    //   setValue(`cart_items.${index}.confidence`, 100);
    //   setValue(`cart_items.${index}.originalStatus`, watch(`cart_items.${index}.lineStatus`));
  }, []);
  
  useEffect(() => {
    // Load initial options from matched_products when component mounts
    const matchedProducts = watch(`cart_items.${index}.matched_products`);
    if (matchedProducts) {
      try {
        const productIds = matchedProducts;
        if (Array.isArray(productIds) && productIds.length > 0) {
          // Map product IDs to actual product objects
          const matchedProductObjects = productIds
            .map(id => productMapping[id])
            .filter(product => product); // Filter out any undefined products
          
          setInitialOptions(matchedProductObjects);
        }
      } catch (e) {
        console.error("Error parsing matched_products:", e);
      }
    }
  }, [watch(`cart_items.${index}.matched_products`), productMapping]);

  useEffect(() => {
    const selectedProduct = watch(`cart_items.${index}.selected_products`);
    if (selectedProduct?.length > 0) {
      const product = productMapping[selectedProduct[0]];
      setValue(`cart_items.${index}.descriptionObj`, product);
      setValue(`cart_items.${index}.qty_um`, product.QUM_Dropdown_Options.split(","));
      
      // Only set qty_unit if 'Ea' is not in QUM options
      const qumOptions = product.QUM_Dropdown_Options.split(",");
      if (!qumOptions.includes(watch(`cart_items.${index}.qty_unit`))) {
        setValue(`cart_items.${index}.qty_unit`, qumOptions[0]);
      }
    }
  }, [watch(`cart_items.${index}.selected_products`)]);

  useEffect(() => {
    const description = getValues(`cart_items.${index}.descriptionObj`);


    // if (referenceData) {
    //   setValue(
    //     `cart_items.${index}.domesticMaterialOnly`,
    //     false
    //   );
    // }
    if (isDescriptionObjPresent) {
      if (!description.domestic_material_only) {
        seShowDomesticMaterialOnly(false);
        setValue(`cart_items.${index}.domesticMaterialOnly`, null);
      } else {
        seShowDomesticMaterialOnly(true);
      }
    } else {
      seShowDomesticMaterialOnly(true);
    }
  }, [referenceData, watch(`cart_items.${index}.descriptionObj`)]);

  useEffect(() => {
    const product_tag = getValues(`cart_items.${index}.product_tag`);
    const description = getValues(`cart_items.${index}.descriptionObj`);

    if (isDescriptionObjPresent) {
    //   setValue(`cart_items.${index}.description`, description.UI_Description);
      setValue(`cart_items.${index}.shape`, description.Key2);
      setValue(`cart_items.${index}.product_id`, description.Product_ID);
      setValue(`cart_items.${index}.reference_product_id`, description.id);
      setValue(`cart_items.${index}.qty_um`, description.QUM_Dropdown_Options.split(","));
      const qumOptions = description.QUM_Dropdown_Options.split(",");
      if (!qumOptions.includes(watch(`cart_items.${index}.qty_unit`))) {
        setValue(`cart_items.${index}.qty_unit`, qumOptions[0]);
      }
      if (!description.domestic_material_only) {
        seShowDomesticMaterialOnly(false);
        setValue(`cart_items.${index}.domesticMaterialOnly`, null);
      } else {
        seShowDomesticMaterialOnly(true);
      }
    } else {
      seShowDomesticMaterialOnly(true);
    }

    if (isDescriptionObjPresent) {
      if (userPartData && Object.keys(userPartData)?.length) {
        let isMappingExist = false;
        const i = Object.keys(userPartData).findIndex((key)=>{
          if(userPartData[key] === product_tag){
            isMappingExist = true;
            if(+key === description.Product_ID){
               return true;
            }
          }
        })
        setSameTagErrorMsg(i > -1 ? null : SAME_TAG_ERROR_MESSAGE);
        if (!isMappingExist) {
          setSameTagErrorMsg(null);
        }
      }
    } else {
      setSameTagErrorMsg(null);
      if(!watch(`cart_items.${index}.product_tag`)){
        setValue(`cart_items.${index}.product_tag`, "");
      }
    }
  }, [
    watch(`cart_items.${index}.product_tag`),
    watch(`cart_items.${index}.descriptionObj`),
  ]);


  useEffect(()=>{
    const atLeastOneValid = watch('cart_items').some((item: any, index: number) => {
      const lineStatus = watch(`cart_items.${index}`)?.lineStatus;
      return (
          bomSummaryViewFilter === 'all' ||
          (bomSummaryViewFilter === 'red' && lineStatus === uploadBomConst.lineItemStatus.pending) ||
          (bomSummaryViewFilter === 'green' && lineStatus !== uploadBomConst.lineItemStatus.pending)
      );
    });
    if(!atLeastOneValid){
      setBomSummaryViewFilter('all');
    }
  },[watch(`cart_items.${viewLineStatusIndex}.lineStatus`)]);

  
  
  useEffect(() => {
    if (errors?.cart_items?.[index]?.qty) {
      setIsQtyInEditMode(true);
    }
  }, [
    errors?.cart_items?.[index]?.qty,
  ]);


useEffect(() => {
  if(selectedProduct && selectedProduct.UI_Description === descriptionInput && lineSessionId === getValues(`cart_items.${index}.line_session_id`)){
    setEnableRejectSearchAnalytic(false)
    handleCreatePOSearch(searchStringData, 'Accept', getValues(`cart_items.${index}.line_session_id`))
  }
},[selectedProduct])

useEffect(() => {
  if((!selectedProduct && descriptionInput.length >= MinSearchDataLen) || (descriptionInput.length >= MinSearchDataLen)){
    setSearchString(descriptionInput)
    setLineSessionId(getValues(`cart_items.${index}.line_session_id`))
  }
  if(descriptionInput.length === 1 ){
    setEnableRejectSearchAnalytic(true)
  }
  if(descriptionInput.length === 0 && searchStringData.length !== 0 && searchStringData.length >= MinSearchDataLen && enableRejectSearchAnalytic){
    handleCreatePOSearch(searchStringData, 'Reject', getValues(`cart_items.${index}.line_session_id`))
    setEnableRejectSearchAnalytic(false)
  }
},[descriptionInput])

  function display(data) {
    const lines = data.UI_Description.split("\n");
    const firstLine = lines[0];
    const restLines = lines.slice(1);

    return (
      <>
        <p className={clsx('liFisrtLine', styles.liFisrtLine)}>{firstLine}</p>
        {restLines.map((line, index) => (
          <p key={index}>{line}</p>
        ))}
      </>
    );
  }

  const qtyEditModeCloseHandler = () => {
    if (
      getValues(`cart_items.${index}.qty`) &&
      !errors?.cart_items?.[index]?.qty
    ) {
      setIsQtyInEditMode(false);
    } else {
      setIsQtyInEditMode(true);
    }
  };

  const descriptionEditModeCloseHandler = () => {
   setIsDescriptionModeEnabled(false)
  };

  const quantityChangeHandler = (event:any) => {
    quantitySizeValidator(event,index);
    validateSavedBomCreatePo(index);
  }

  const dollerPerUmFormatter = (umVal) => {
    const price_unit = getValues(`cart_items.${index}.price_unit`);
    return formatDollarPerUnit(price_unit,umVal,index);
  }

  const saveUserActivity = (isRemovingLine = false) => {
    if(watch(`cart_items.${index}.descriptionObj`)){
    //   saveUserLineActivity(sessionId, isRemovingLine, index);
    }
  }

  const getPlaceholder = (index) => {
    const cartItems = watch('cart_items');
    const firstEmptyIndex = cartItems ? cartItems.findIndex(item => !item?.descriptionObj || !item?.descriptionObj?.UI_Description) : -1;
    const matchedProducts = watch(`cart_items.${index}.matched_products`);
    const matchCount = matchedProducts ? matchedProducts.length : 0;
    if (focusPlaceHolder) {
      return "EX:  BEAM 4\" x 13 . . .";
    } else if (index === firstEmptyIndex && !orderInfoIsFilled) {
      return "ALL HEADER INFORMATION MUST BE ENTERED BEFORE ENTERING LINE ITEMS OR UPLOADING A BOM";
    } else if (matchCount === 0 && !hasSingleMatchedProduct && !watch(`cart_items.${index}.description`)) {
      return "Unable to recognize data.\nNo product matches found.\nClick to search available products.";
    } else if (matchCount === 0 && !hasSingleMatchedProduct) {
      return "No product matches found.\nClick to search available products.";
    } else if(!hasSingleMatchedProduct) {
      return "Exact match not found.\n" +
             `Click to view the ${matchCount} Matches\n` + 
             "or click to search products.";
    } else {
        return "";
    }
  };

    const saveBomDraft = () => {
      if(currentBomData?.id){
        setBomDataIdToRefresh(currentBomData?.id);
      }
        const bomTileData = { ...watch(`cart_items.${index}`) }
        const payload = {
            "data": {
                "id": bomTileData.bom_line_id,
                "selected_product_id": bomTileData.product_id ?? null,
                "status": bomTileData.lineStatus,
                "qty": bomTileData.qty,
                "qty_unit": getFormattedUnit(bomTileData.qty_unit),
                "domestic_material_only": bomTileData?.domesticMaterialOnly ?? false,
                "product_tag": bomTileData?.product_tag ?? null,
            }
        }
        postBomDraft(payload);
    }

  const lineStatusMouseEnterHandler = () => {
    const item = watch(`cart_items.${index}`);
    if(!(item?.descriptionObj?.UI_Description && Number(item?.qty) > 0)){
      setDisableApproveButton(true);
    }else{
      setDisableApproveButton(false);
    }
  }

  // Add global keyboard shortcut listener
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'S' && isMouseOverComponent) {
        const searchString = bomTileDefaultData?.search_string ?? 'No Search Term Found';
        showCommonDialog(null, searchString, null, resetDialogStore, [{name: commomKeys.errorBtnTitle, action: resetDialogStore}]);
        e.preventDefault();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isMouseOverComponent, bomTileDefaultData, resetDialogStore]);

  const saveModifiedBom = (input: any) => {
    try {
      if(currentBomData?.id && watch(`cart_items.${index}`)?.bom_line_id){
        const data ={
          [currentBomData?.id]: {lineId: watch(`cart_items.${index}`)?.bom_line_id, input: input}
        }
        const _lastModifiedBom = localStorage.getItem(localStorageKeys.lastModifiedBom);
        if(_lastModifiedBom){
          const _lastModifiedBomData = JSON.parse(_lastModifiedBom);
          _lastModifiedBomData[currentBomData?.id] = {lineId: watch(`cart_items.${index}`)?.bom_line_id, input: input};
          localStorage.setItem(localStorageKeys.lastModifiedBom, JSON.stringify(_lastModifiedBomData));
        }else{
          localStorage.setItem(localStorageKeys.lastModifiedBom, JSON.stringify(data));
        }
      }
    } catch (e) {
      console.warn('Could not store value in localStorage', e);
    }
  }

  const handleQtyInputRef = (e:any) => {
    qtyRef.current = e;
  }
  const handleProductTagInputRef = (e:any) => {
    productTagRef.current = e;
  }


  useEffect(() => {
    if(qtyRef?.current && isQtyInEditMode){
      qtyRef.current.blur();
    }
    if(productTagRef?.current && isDescriptionModeEnabled){
      productTagRef.current.blur();
    }
    setIsDescriptionModeEnabled(false)
  }, [viewIndex]);

  return (
    <tr className={styles.marginBottom} id={watch(`cart_items.${index}`)?.bom_line_id} ref={watch(`cart_items.${index}`)?.last_updated_product  ? lastModifiedBomRef : null}
        onMouseEnter={() => {setIsMouseOverComponent(true); setIsHovered(true)}}
        onMouseLeave={() => {setIsMouseOverComponent(false); setIsHovered(false)}}>
      <td>
        <div className={styles.prodId}>
          {/* <span className={styles.numberContainer}>{index + 1}</span> */}
          <div className={clsx( styles.activeInactiveDot , watch(`cart_items.${index}.lineStatus`) === 'PENDING' ? styles.inactive : styles.active)}></div>

          <div className={styles.domesticMaterialCheckbox} >
            <label className={styles.lineNumberContainer} data-hover-video-id={ orderInfoIsFilled ? "domestic-only-po" : ""}>
              <input
                type="checkbox"
                {...register(`cart_items.${index}.domesticMaterialOnly`)}
                checked={watch(`cart_items.${index}.domesticMaterialOnly`) ?? false}
                onChange={(e) => {
                  if (!showDomesticMaterialOnly || !(isDescriptionObjPresent)) {
                    return; // Prevent changes when visually disabled
                  }
                  register(`cart_items.${index}.domesticMaterialOnly`).onChange(e);
                  saveBomDraft();
                }}
                className={styles.hiddenCheckbox}
                // We keep it enabled for keyboard navigation but mark it as aria-disabled
                aria-disabled={(!showDomesticMaterialOnly || (!(isDescriptionObjPresent)))}
              />
              <span
                className={clsx(
                  styles.customNumberToggle,
                  watch(`cart_items.${index}.domesticMaterialOnly`) ? styles.active : "",
                  (!showDomesticMaterialOnly || (!(isDescriptionObjPresent))) ? styles.disabled : ""
                )}
                onBlur={() => {
                  setIsDomesticFocused(false);
                }}
                onFocus={() => {
                  setIsHovering(false); // Turn off hover style when focused
                  setIsDomesticFocused(true);
                }}
                onKeyDown={(e) => {
                  // Always become active when receiving keyboard focus, even when visually disabled
                  setIsDomesticFocused(true);
                  
                  // Allow toggling with Enter or Space only when not visually disabled
                  const isVisuallyDisabled = !showDomesticMaterialOnly || !(isDescriptionObjPresent);
                      
                  if ((e.key === 'Enter' || e.key === ' ') && !isVisuallyDisabled) {
                    e.preventDefault();
                    const newValue = !(watch(`cart_items.${index}.domesticMaterialOnly`) ?? false);
                    setValue(`cart_items.${index}.domesticMaterialOnly`, newValue);
                    saveBomDraft();
                  }
                  
                  // Handle tab navigation
                   if (e.key === 'Tab' && !e.shiftKey) {
                    // Forward tab to next element
                    setIsDomesticFocused(false);
                  }
                }}
                role="checkbox"
                id={"domesticMaterialCheckbox"+index}
                aria-checked={watch(`cart_items.${index}.domesticMaterialOnly`) ?? false}
                aria-disabled={(!showDomesticMaterialOnly || (!(isDescriptionObjPresent)))}
                ref={lineNumberRef}
                tabIndex={(!showDomesticMaterialOnly || (!(isDescriptionObjPresent))) ? -1 : 0}
              >
                {index + 1}
              </span>
              <span className={clsx(styles.usaOnlyText, watch(`cart_items.${index}.domesticMaterialOnly`) ? "" : styles.visibilityHidden)}>USA<br />ONLY</span>
            </label>
          </div>
          {(isHovering && showDomesticMaterialOnly && !watch(`cart_items.${index}.domesticMaterialOnly`)) && <DomesticCheckTextIcon className={styles.domesticCheckText} />}
        </div>
      </td>
      <td>
       
          {!bomTileDefaultData?.description ? <div className={styles.noDataFoundBOM}>(no data found)</div> 
          :  
          <div className={styles.searchString}>
            {(bomTileDefaultData?.description ? bomTileDefaultData?.description + " " : "") + 
            (bomTileDefaultData?.specification ? bomTileDefaultData?.specification + " " : "") + 
            (bomTileDefaultData?.grade ? bomTileDefaultData?.grade + " " : "") + 
            (bomTileDefaultData?.length ? bomTileDefaultData?.length : "")}
           </div>}
           {
          watch(`cart_items.${index}.lineStatus`) !== 'SKIPPED' ?
           ( 
        <div className={clsx(styles.poDescriptionDiv, (isOrderLineDisabled) && styles.disabled)} onFocus={() => setIsHovered(true)} onBlur={() => setIsHovered(false)}>
          {isDescriptionModeEnabled ? (
            <ClickAwayListener onClickAway={descriptionEditModeCloseHandler}>
              <div id={watch(`cart_items.${index}`)}>
                <Controller
                  name={register(`cart_items.${index}.descriptionObj`).name}
                  control={control}
                  render={({ field: { ...rest } }) => (
                    <Autocomplete
                      id={`combo-box-demo${index}`}
                      disabled={apiCallInProgress}
                      open={openDescription}
                      onClose={(e) => {
                        if (e?.keyCode === 27) {
                          setDescriptionInput('');
                        }
                        // setOpenDescription(false);
                      }}
                    //   onOpen={() => setOpenDescription(true)}
                    //   openOnFocus={!(watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0)}
                      forcePopupIcon={false}
                      options={products?.length ? products : []}
                      inputValue={descriptionInput}
                      onInputChange={(_, value, reason) => {
                        if(value.length > 1 && reason === "input") setOpenDescription(true);
                        setDescriptionInput(value); setDisableBidBuyNow(true)
                      }}
                      value={watch(`cart_items.${index}.descriptionObj`)}
                      disablePortal
                      PaperComponent={({ children, ...props }) => (
                        <div
                          {...props}
                          style={{
                            ...props.style,
                            borderRadius: '0px 0px 13px 13px',
                            maxHeight: '400px',
                            overflow: 'auto'
                          }}
                        >
                          {children}
                        </div>
                      )}
                      componentsProps={{
                        popper: {
                          modifiers: [
                            {
                              name: 'flip',
                              enabled: filteredItemIndex  === (filterFieldsLength - 1)
                            }
                          ]
                        }
                      }}
                      classes={{
                        root: styles.autoCompleteDescBom,
                        popper: clsx(styles.autocompleteDescPanelBom, (hasDescriptionButNoMatches && !hasSearchResults) && styles.noOptionListPanelBom),
                        paper: clsx(styles.autocompleteDescInnerPanelBom, (hasDescriptionButNoMatches  && !hasSearchResults) && styles.noOptionListInnerPanelBom),
                        listbox: styles.listAutoComletePanelBom,
                        noOptions: clsx(styles.noOptionPanelBom, ((descriptionInput && descriptionInput.length > 1) || hasDescriptionButNoMatches) && styles.visibleNoOptionBom),
                      }}
                      ListboxComponent={ListboxComponent}
                      // nooptionscomponent={NoOptionsComponent}
                      noOptionsText={(descriptionInput && descriptionInput.length > 1)
                        ? <span className={styles.matchesfoundSearch}>
                          <span className={styles.textStyle3}> No Options</span>
                        </span>
                        : hasDescriptionButNoMatches &&
                        <span className={styles.matchesfoundSearch}>
                          <span className={styles.textStyle1}>0</span>
                          <span className={styles.textStyle2}> Matches </span><span className={styles.textStyle3}> found.<br /> Search available products.</span>
                        </span>
                      }
                      getOptionDisabled={() => false}
                      filterOptions={(options, state) => {
                        const searchTerm = state.inputValue.trim();
                        if (searchTerm.length <= 1) {
                          // Return initialOptions when user hasn't typed a search term
                          setHasSearchResults(Boolean(initialOptions?.length));
                          return initialOptions;
                        }

                        const filteredResults = searchProducts(
                          options,
                          getValidSearchData(searchTerm),
                          searchTerm,
                          userPartData,
                          true
                        );

                        setHasSearchResults(Boolean(filteredResults?.length));
                        return filteredResults;
                      }}
                      isOptionEqualToValue={(option, value) => {
                        if (!option || !value) return false;
                        return option.UI_Description === value.UI_Description;
                      }}
                      getOptionLabel={(item) => item?.UI_Description?.toUpperCase() || ""}
                      renderInput={(params) => (
                        <div ref={params.InputProps.ref}>
                          <textarea
                            data-hover-video-id={ orderInfoIsFilled ? "entering-a-line-in-po" : ""}
                            {...params.inputProps}
                            name={register(`cart_items.${index}.descriptionObj`).name}
                            type="text"
                            placeholder={getPlaceholder(index)}
                            className={clsx(
                              styles.poDescription,
                            //   styles.poDescriptionOpen,
                            !(isDescriptionObjPresent) && styles.poDescriptionOpen,
                              (watch(`cart_items.${index}.descriptionObj`)?.UI_Description?.length > 0) && styles.poDescriptionFirstLine,
                              (LargeProductsNameList?.find((largeProductsName)=> watch(`cart_items.${index}.descriptionObj`)?.UI_Description?.split("\n")[0]?.includes(largeProductsName))) && styles.poDescriptionFirstLine1
                            )}
                            onFocus={() => {
                              params.inputProps.onFocus();
                              setFocusPlaceHolder(true);
                              setIsHovering(true);
                              openAddLineTab();
                              setOpenDescription(hasDescriptionButNoMatches || (initialOptions.length > 0 && !(isDescriptionObjPresent)));
                            }}
                            onBlur={() => {
                              params.inputProps.onBlur();
                              setFocusPlaceHolder(false);
                              setIsHovering(false);
                              setIsDescriptionModeEnabled(false);
                            }}
                            onKeyDown={(e) => {
                                if (e.ctrlKey && e.shiftKey && e.key === 'S') {
                                    const searchString = bomTileDefaultData?.search_string ?? 'No Search Term Found'
                                    showCommonDialog(null, searchString, null, resetDialogStore, [{name: commomKeys.errorBtnTitle, action: resetDialogStore}])
                                    return;
                                }
                              if (e.key === 'Tab') {
                                if (e.shiftKey) {
                                  const isCheckboxDisabled = !showDomesticMaterialOnly ||
                                    !(watch(`cart_items.${index}.descriptionObj`) &&
                                      Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0);

                                  // Skip focusing the domestic material checkbox if it's disabled
                                  if (isCheckboxDisabled) {
                                    return; // Let default tab behavior work
                                  }

                                  e.preventDefault();
                                  if (lineNumberRef && lineNumberRef.current) {
                                    setIsHovering(false);
                                    setIsDomesticFocused(true);
                                    lineNumberRef.current.focus();
                                  }
                                } 
                                else {
                                  if (openDescription && hasSearchResults) {
                                    e.preventDefault();
                                    const inputValue = params.inputProps.value;
                                    const activeElement = document.activeElement;
                                    
                                    // Get the aria-activedescendant attribute to find the currently focused option
                                    const activeDescendantId = activeElement.getAttribute('aria-activedescendant');
                                    
                                    if (activeDescendantId) {
                                      // Extract the option index from the ID (e.g., "combo-box-demo0-option-1" => 1)
                                      const optionIndexMatch = activeDescendantId.match(/option-(\d+)$/);
                                      if (optionIndexMatch && optionIndexMatch[1]) {
                                        const optionIndex = parseInt(optionIndexMatch[1]);
                                        // Get the filtered products based on the current input
                                        const options = products?.length ? products : [];
                                        const filteredOptions = searchProducts(
                                          options,
                                          getValidSearchData(inputValue),
                                          inputValue,
                                          userPartData,
                                          true
                                        );   
                                        // Get the selected item using the option index
                                        if (optionIndex >= 0 && optionIndex < filteredOptions.length) {
                                          const selectedItem = filteredOptions[optionIndex];
                                          // Update the form value with the selected item
                                        //   updateLineProduct(index, selectedItem);
                                          setValue(`cart_items.${index}.descriptionObj`, selectedItem);
                                          updateLineProductTag(index, selectedItem);
                                          if(selectedItem) {
                                            saveBomDraft();
                                            saveModifiedBom("description");
                                          }
                                          setOpenDescription(false);
                                          setDescriptionInput(selectedItem?.UI_Description?.toUpperCase() || "");
                                          setIsDescriptionModeEnabled(false);
                                          setIsQtyInEditMode(true);
                                          // Focus on the quantity field after selection
                                          setTimeout(() => {
                                            const qtyInput = document.querySelector(`input[name="cart_items.${index}.qty"]`);
                                            if (qtyInput instanceof HTMLElement) {
                                              qtyInput.focus();
                                            }
                                          }, 100);
                                        }
                                      }
                                    }
                                  } 
                                }

                                setOpenDeliveryToDialog(false);
                                setTimeout(() => {
                                  const descriptionInput = document.querySelector('input[name="shipping_details.zip"]');
                                  if (descriptionInput instanceof HTMLElement) {
                                    descriptionInput.focus();
                                  }
                                }, 100);
                              }
                            }}
                            autoFocus
                          />
                        </div>
                      )}
                      {...rest}
                      onChange={(_, item, reason) => {
                        if (item) {
                          setIsQtyInEditMode(true);
                          rest.onChange(item);
                          updateLineProductTag(index, item);

                          // Focus on the quantity field after a short delay
                          setTimeout(() => {
                            validateSavedBomCreatePo(index);
                            const qtyInput = document.querySelector(`input[name="cart_items.${index}.qty"]`);
                            if (qtyInput instanceof HTMLElement) {
                              qtyInput.focus();
                            }
                          }, 100);
                        }

                        if(reason === 'clear' && watch(`cart_items.${index}.lineStatus`) !== uploadBomConst.lineItemStatus.approved){
                            setDescriptionInput('');
                            setValue(`cart_items.${index}.descriptionObj`, {});
                            setValue(`cart_items.${index}.product_id`, null);
                            setValue(`cart_items.${index}.qty`, bomTileDefaultData?.qty);
                            setValue(`cart_items.${index}.qty_unit`, bomTileDefaultData?.qty_unit);
                            setValue(`cart_items.${index}.qty_um`, null);
                            clearErrors(`cart_items.${index}.qty`);
                            if(initialOptions.length > 0){
                                setOpenDescription(true)
                            }
                        }else{
                            setOpenDescription(false)
                        }
                      }}
                      onBlur={(e) => {
                        setOpenDescription(false)
                        saveBomDraft();
                        saveModifiedBom("description");
                        rest.onBlur(e);
                      }}
                      renderOption={(props, option) => (
                        <span key={option.id} {...props} className={clsx(styles.productDescription,LargeProductsNameList?.find((largeProductsName)=> option.UI_Description?.split("\n")[0]?.includes(largeProductsName)) && styles.miscellaneousText)}>
                          {display(option)}
                        </span>
                      )}
                    />
                  )}
                />
              </div>
            </ClickAwayListener>
          )
          :
            <p className={styles.descriptionModeDisabled} data-hover-video-id={ orderInfoIsFilled ? "entering-a-line-in-po" : ""}>
              {(!watch(`cart_items.${index}.descriptionObj`)?.UI_Description && !(index === firstEmptyIndex && !orderInfoIsFilled)) && 
                <div className={styles.textareaOverlay}
              onClick={() => {
                if (!isOrderLineDisabled) {
                setIsDescriptionModeEnabled(true);
                // If there's existing search data, show filtered results
                if (descriptionInput.length > 1) {
                  const searchTerm = descriptionInput.trim();
                  const filteredResults = searchProducts(
                    products?.length ? products : [],
                    getValidSearchData(searchTerm),
                    searchTerm,
                    userPartData,
                    true
                  );
                  setHasSearchResults(Boolean(filteredResults?.length));
                } else {
                  // Show initialOptions if we have them
                  setHasSearchResults(Boolean(initialOptions?.length));
                }
            }
              }}
              >
                {(watch(`cart_items.${index}.matched_products`) && (watch(`cart_items.${index}.matched_products`).length) === 0 && !hasSingleMatchedProduct) ?
                <>
                    {!watch(`cart_items.${index}.description`) && (
                      <>
                        Unable to recognize data.
                        <br />
                      </>
                    )}
                    No product matches found. <br /> Click to search available products.
                </>
                :
                <>
                    Exact match not found.<br />
                    Click to view the <span className={styles.childCount}>{initialOptions.length} </span><span className={styles.matches}>{initialOptions.length === 1 ? 'Match' : 'Matches'}<br /></span>
                    or click to search products.
                </>
                }
              </div>
}
              <textarea
              data-hover-video-id={ orderInfoIsFilled ? "entering-a-line-in-po" : ""}
                type="text"
                name={register(`cart_items.${index}.descriptionObj`).name}
                id={`combo-box-demo${index}`}
                value={watch(`cart_items.${index}.descriptionObj`)?.UI_Description?.toUpperCase() || ''}
                placeholder={getPlaceholder(index)}
                className={clsx(styles.poDescription, (watch(`cart_items.${index}.descriptionObj`)?.UI_Description?.length > 0) && styles.poDescriptionFirstLine,
               (LargeProductsNameList?.find((largeProductsName)=> watch(`cart_items.${index}.descriptionObj`)?.UI_Description?.split("\n")[0]?.includes(largeProductsName))) && styles.poDescriptionFirstLine1)
              }
                onClick={() => {
                  setOpenDescription(true);
                  setIsDescriptionModeEnabled(true);
                  // If there's existing search data, show filtered results
                  if (descriptionInput.length > 1) {
                    const searchTerm = descriptionInput.trim();
                    const filteredResults = searchProducts(
                      products?.length ? products : [],
                      getValidSearchData(searchTerm),
                      searchTerm,
                      userPartData,
                      true
                    );
                    setHasSearchResults(Boolean(filteredResults?.length));
                  } else {
                    // Show initialOptions if we have them
                    setHasSearchResults(Boolean(initialOptions?.length));
                  }
                }}
                onFocus={() => {
                  setIsHovering(true);
                  setIsDescriptionModeEnabled(true)
                  openAddLineTab()
                }}
                onBlur={(e) => {
                  setIsHovering(false);
                }}
                onKeyDown={(e) => {
                    if (e.ctrlKey && e.shiftKey && e.key === 'S') {
                        const searchString = bomTileDefaultData?.search_string ?? 'No Search Term Found'
                        showCommonDialog(null, searchString, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
                        return;
                    }

                  if(e.key === 'Tab' && e.shiftKey){
                    const isCheckboxDisabled = !showDomesticMaterialOnly || 
                      !(watch(`cart_items.${index}.descriptionObj`) && 
                        Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0);
                    
                    // Skip focusing the domestic material checkbox if it's disabled
                    if (isCheckboxDisabled) {
                      return; // Let default tab behavior work
                    }
                    
                    e.preventDefault();
                    if (lineNumberRef && lineNumberRef.current) {
                      setIsHovering(false);
                      setIsDomesticFocused(true);
                      lineNumberRef.current.focus();
                    }
                    
                    setOpenDeliveryToDialog(false);
                    setTimeout(() => {
                      const descriptionInput = document.querySelector('input[name="shipping_details.zip"]');
                      if (descriptionInput instanceof HTMLElement) {
                        descriptionInput.focus();
                      }
                    }, 100);
                  }
                }}
                disabled={(isOrderLineDisabled)}
                readOnly
              />
            </p>
          }
            {watch(`cart_items.${index}.descriptionObj`)?.UI_Description &&
              <Tooltip
                title={
                  sameTagErrorMsg && (
                    <span
                      dangerouslySetInnerHTML={{ __html: sameTagErrorMsg }}
                    ></span>
                  )
                }
                arrow
                placement={"bottom-end"}
                disableInteractive
                TransitionComponent={Fade}
                TransitionProps={{ timeout: 200 }}
                classes={{
                  tooltip: "partNumberTooltip",
                }}
              >
                <div className={styles.partNumberFiled}>
                  <InputWrapper>
                    <CustomTextField
                      dataHoverVideoId="part-number-po"
                      inputRef={handleProductTagInputRef}
                      type='text'
                      register={register(`cart_items.${index}.product_tag`)}
                      disabled={apiCallInProgress}
                      placeholder="Add YOUR PART #"
                      className={clsx({ [styles.errorInput]: errors?.cart_items?.[index]?.product_tag?.message })}
                      value={watch(`cart_items.${index}.product_tag`) ?? ""}
                      onFocus={() => {
                        setIsHovering(true);
                        setIsDescriptionModeEnabled(false);
                      }}
                      onBlur={(e) => {
                        register(`cart_items.${index}.product_tag`).onBlur(e);
                        saveBomDraft();
                        saveModifiedBom("product_tag");
                        setIsHovering(false);
                        // setIsQtyInEditMode(true);
                      }}
                      onKeyUp={(e) => {
                        setValue(
                          `cart_items.${index}.product_tag`,
                          e.target.value
                        );
                      }}
                    />
                  </InputWrapper>
                </div>
              </Tooltip>
            }

        </div>
           ) : (
            <span className={styles.skippedPlaceHolderText}>PLACEHOLDER - This line has been skipped.</span>
           )

        }
      </td>
      <td>
      <div className={styles.priceQty}>
            {bomTileDefaultData?.qty}
        </div>
        {
              watch(`cart_items.${index}.lineStatus`) !== 'SKIPPED'  && (
      <div className={clsx(styles.poQty,styles.poQtyBOM)}>
          <div onFocus={() => setIsHovered(true)} onBlur={() => setIsHovered(false)}>
            {isQtyInEditMode ? (
              <ClickAwayListener  onClickAway={qtyEditModeCloseHandler}>
                <Tooltip
                  title={ watch(`cart_items.${index}.qty`) !== null &&
                    errors?.cart_items?.[index]?.qty?.message
                  }
                  arrow
                  placement={"bottom-start"}
                  disableInteractive
                  TransitionComponent={Fade}
                  TransitionProps={{ timeout: 200 }}
                  classes={{
                    tooltip: "inputQtyTooltip",
                  }}
                >
                  <div>
                    <InputWrapper>
                      <CustomTextField 
                        type='text'
                        inputRef={handleQtyInputRef}
                        register={register(`cart_items.${index}.qty`)}
                        // disabled={!(watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0)}
                        value={watch(`cart_items.${index}.qty`) ?? ""}
                        onFocus={() => {
                          setIsHovering(true);
                          openAddLineTab();
                        }}
                        className={clsx(errors?.cart_items?.[index]?.qty?.message && styles.errorQty)}
                        onChange={(e) => {
                          quantityChangeHandler(e);
                        }}
                        onBlur={(e) => {
                          register(`cart_items.${index}.qty`).onBlur(e);
                          qtyEditModeCloseHandler();
                          saveBomDraft();
                          setIsHovering(false);
                          saveModifiedBom("qty");
                        }}
                        errorInput={errors?.cart_items?.[index]?.qty?.message}
                        mode={watch(`cart_items.${index}.qty_unit`) === priceUnits.pc ? "wholeNumber" : "number"}
                        dataHoverVideoId="entering-a-line-in-po"
                        // autoFocus={true}
                      />
                    </InputWrapper>
                  </div>
                </Tooltip>
              </ClickAwayListener>
            ) : (
                <p
                  className={styles.poQtyValue}
                >
                  <input type="text" id={`qty-input-${index}`} className={styles.poQtyValue} value={(watch(`cart_items.${index}.qty`) && watch(`cart_items.${index}.qty_unit`) !==  priceUnits.pc) ? formatToTwoDecimalPlaces(watch(`cart_items.${index}.qty`)) : watch(`cart_items.${index}.qty`)}
                    onClick={() => {
                      setIsQtyInEditMode(true);
                      setTimeout(() => {
                        const qtyInput = document.querySelector(`input[name="cart_items.${index}.qty"]`);
                        if (qtyInput instanceof HTMLElement) {
                          qtyInput.focus();
                        }
                      }, 100);
                    }}
                    onFocus={() => {
                      setIsHovering(true);
                      setIsQtyInEditMode(true);
                      setTimeout(() => {
                        const qtyInput = document.querySelector(`input[name="cart_items.${index}.qty"]`);
                        if (qtyInput instanceof HTMLElement) {
                          qtyInput.focus();
                        }
                      }, 100);
                    }}
                    onBlur={() => {
                      setIsHovering(false);
                    }}
                    data-hover-video-id={ orderInfoIsFilled ? "entering-a-line-in-po" : ""}
                    readOnly />
                </p>
              )}
          </div>
          {/* Always show the quantity_unit dropdown */}
          <span className={clsx(styles.selectUom,styles.selectUomBOM)}>
            <CustomMenu
              name={register(`cart_items.${index}.qty_unit`).name}
              control={control}
              disabled={apiCallInProgress}
              onChange={(e) => {
                if(e.target.value === priceUnits.pc){
                  setValue(`cart_items.${index}.qty`, watch(`cart_items.${index}.qty`).replace(/(\d+)\.([5-9])\d*/g, (_, whole, decimal) => String(Number(whole) + 1)).replace(/(\d+)\.([0-4])\d*/g, (_, whole) => whole));
                }
                validateSavedBomCreatePo(index);
                saveBomDraft();
              }}
              items={getValues(`cart_items.${index}.qty_um`)?.map((x) => ({ title: x, value: x })) ?? QUANTITY_UNITS}
              className={styles.uomDrodownBOM}
              MenuProps={{
                classes: {
                  paper: styles.selectUomPaper,
                },
                id: `qty-unit-menu-${index}` // Add index to make ID unique
              }}
              onKeyDown={(e) => {
                if (e.key === 'Tab' && document.activeElement?.closest(`#qty-unit-menu-${index}`)) { // Update selector to match new ID
                  const value = document.activeElement.textContent;
                  if(value === priceUnits.pc){
                    setValue(`cart_items.${index}.qty`, watch(`cart_items.${index}.qty`).replace(/(\d+)\.([5-9])\d*/g, (_, whole, decimal) => String(Number(whole) + 1)).replace(/(\d+)\.([0-4])\d*/g, (_, whole) => whole));
                  }
                  setValue(`cart_items.${index}.qty_unit`, value);
                  validateSavedBomCreatePo(index);
                  saveBomDraft();
                }
              }}
              // Find the matching title based on case-insensitive comparison
              renderValue={(value) => {
                if (!value) return <span className='dropdownPlaceholderText'>PC</span>;
                const unit = QUANTITY_UNITS.find(u => u.value.toLowerCase() === value.toLowerCase());
                return unit ? unit.title : value;
              }}
              placeholder={"PC"}
            />
          </span>
        </div> )}
      </td>
      <td>
        <div className={styles.lineStatusContainer} onFocus={() => lineStatusMouseEnterHandler()} onMouseEnter={() => lineStatusMouseEnterHandler()}>
            {viewLineStatusIndex >= viewIndex && viewLineStatusIndex < viewIndex+5 && <LineStatus status={watch(`cart_items.${index}.lineStatus`)} originalStatus={watch(`cart_items.${index}.originalStatus`)} setStatus={(status) => {setValue(`cart_items.${index}.lineStatus`, status)}} confidence={watch(`cart_items.${index}.confidence`)} saveBomDraft={saveBomDraft} isHovered={isHovered} disableApproveButton={disableApproveButton} />}
        </div>
      </td>
    </tr>
  );
};

export default BomTile;
