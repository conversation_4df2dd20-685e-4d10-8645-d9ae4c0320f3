import React, { useEffect, useState } from 'react'
import styles from './PoHistoryRightWindow.module.scss'
import clsx from 'clsx'
import { commomKeys, formatToTwoDecimalPlaces, useGlobalStore } from '@bryzos/giss-ui-library'
import { useRightWindowStore } from 'src/renderer2/pages/RightWindow/RightWindowStore'
import usePostCancelPo from 'src/renderer2/hooks/usePostCancelPo'
import ChatWithVendor from '../ChatWithVendor/ChatWithVendor'
import usePostStartChat from 'src/renderer2/hooks/usePostStartChat'
import useDialogStore from '../DialogPopup/DialogStore'
import { useChatWithVendorStore } from '../ChatWithVendor/ChatWithVendorStore'
import { ReactComponent as CloseIcon } from '../../assets/New-images/close-icon.svg';

const PoHistoryRightWindow = () => {
    const { userData } = useGlobalStore();
    const { props, isAddLineBtnClicked } = useRightWindowStore();
    const { watch, setIsRemoveLineBtnClicked, isRemoveLineBtnClicked, showSuccessBox, setShowSuccessBox, showConfirmationBox, setShowConfirmationBox, saveAddedLine, handleSuccessBoxClose, handleConfirmationBoxClose, handleAddLineBtnClick } = props;
    const [cancelPoNumber, setCancelPoNumber] = useState(null);
    const { mutateAsync: cancelPo } = usePostCancelPo();
    const [isChatWithVendor, setIsChatWithVendor] = useState(false);

    const {mutateAsync: startChat} = usePostStartChat();

    const {showCommonDialog, resetDialogStore} = useDialogStore();

    const {setChannelName, setPoNumber, setMessages} = useChatWithVendorStore();

    const [isPoClaimed, setIsPoClaimed] = useState(false);

    useEffect(() => {
        const fullfilledBy = watch('fulfilled_by');
        if(!fullfilledBy || fullfilledBy.trim() === ''){
            setIsPoClaimed(false);
        }else{
            setIsPoClaimed(true);
        }
    }, [watch('fulfilled_by')])

    const cancelOrder = async () => {
        const payload = {
            po_number: watch('buyer_po_number'),
            id: watch('id')
        }
        const response = await cancelPo(payload);
        if(response.error_message){
            console.log("error_message ", response.error_message);
            return;
        }
        setShowConfirmationBox(false);
        setShowSuccessBox(true);
    }

    const handleSuccessBoxCancel = () => {
        setCancelPoNumber(null);
        handleSuccessBoxClose()
    }

    const handleConfirmationBoxCancel = () => {
        setCancelPoNumber(null);
        setShowConfirmationBox(false);
        if(!cancelPoNumber) handleConfirmationBoxClose()
    }

    function formatDate(date) {
        const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
                        "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        const month = months[date.getMonth()];
        const day = date.getDate();
        const year = date.getFullYear();
        
        let hours = date.getHours();
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const ampm = hours >= 12 ? "pm" : "am";
        hours = hours % 12 || 12;
        
        return `${month} ${day}, ${year} ${hours}:${minutes}${ampm}`;
    }

    const handleChatWithVendor = async () => {
        const myId = useGlobalStore.getState().userData.data.id;
        if(!isPoClaimed) return;
        const poNumber = watch('buyer_po_number');
        const channelData = await startChat({reference_id: poNumber});
        if(channelData?.error_message){
            console.log("error_message ", channelData.error_message);
            showCommonDialog(null, channelData.error_message , null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
            return;
        }
        let messages = [];
        if(channelData?.chats&&channelData?.chats?.length > 0){
            messages = channelData.chats.map(item=>{
                const message = JSON.parse(item.message);
                message.timestamp = item.message_created_at;
                message.fromName = item.user_name;
                message.fromId = item.user_id;
                message.messageId = item.message_id;
                message.isMyMessage = message.fromId === myId;
                message.formattedTimestamp = formatDate(new Date(message.timestamp));
                return message;
            });
            messages = messages.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
        }
        setMessages(messages);
        setChannelName(poNumber);
        setPoNumber(poNumber);
        setIsChatWithVendor(true);
    }

    return (
        <>
            <div className={styles.claimOrderRightWindow}>
                <div className={styles.claimOrderRightWindowHeader}>
                    <div className={styles.summarySection}>
                        <div className={styles.summaryRow}>
                            <div className={styles.summaryRowLbl}>Material Total</div>
                            <div className={styles.summaryRowNum}>$ {formatToTwoDecimalPlaces(watch('material_total'))}</div>
                        </div>
                        <div className={`${styles.summaryRow} ${styles.muted}`}>
                            <div className={styles.summaryRowLbl}>Sales Tax</div>
                            <div className={styles.summaryRowNum}>$ {formatToTwoDecimalPlaces(watch('sales_tax')) || 'Exempt'}</div>
                        </div>
                        <div className={`${styles.summaryRow} ${styles.muted}`}>
                            <div className={styles.summaryRowLbl}>Deposit</div>
                            <div className={styles.summaryRowNum}>$ {formatToTwoDecimalPlaces(watch('deposit')) || 'Exempt'}</div>
                        </div>
                        <div className={`${styles.summaryRow} ${styles.muted}`}>
                            <div className={styles.summaryRowLbl}>Subscription</div>
                            <div className={styles.summaryRowNum}>$ {formatToTwoDecimalPlaces(watch('subscription')) || '0.00'}</div>
                        </div>
                    </div>
                    <div className={clsx(styles.summaryRow, styles.totalPurchase)}>
                        <div className={styles.totalPurchaseLbl}>Total Purchase</div>
                        <div className={styles.totalPurchaseNum}>$ {formatToTwoDecimalPlaces(watch('totalPurchase'))}</div>
                    </div>

                </div>
                <div className={styles.claimOrderNote}>
                    Some actions may be unavailable due to the
                    timing of order shipment.
                </div>
                <div className={styles.btnSection}>
                    <div>
                        <button onClick={handleAddLineBtnClick} className={isAddLineBtnClicked ? styles.active : ''}>ADD A LINE</button>
                    </div>
                    <div>
                        <button onClick={() => { setIsRemoveLineBtnClicked(!isRemoveLineBtnClicked) }} className={isRemoveLineBtnClicked ? styles.active : ''}>REMOVE A LINE</button>
                    </div>
                    <div>
                        <button onClick={() => { setCancelPoNumber(watch('buyer_po_number')); setShowConfirmationBox(true); }} >CANCEL ORDER</button>
                    </div>
                    <div>
                        <button onClick={handleChatWithVendor} disabled = {!isPoClaimed}>CHAT WITH VENDOR</button>
                    </div>
                </div>
            </div>
            {showSuccessBox && (
                <div className={clsx(styles.claimOrderRightWindow,styles.orderUpdatedSuccesWindow)}>
                    <div className={styles.closeIcon} onClick={handleSuccessBoxCancel}><CloseIcon/></div>
                    {cancelPoNumber ? (
                        <div>
                            <div>
                                <span>YOUR CANCELATION REQUEST HAS BEEN SUBMITTED</span>
                                <span>We have notified {watch('fulfilled_by')} of your request. We will notify you when they respond.</span>
                            </div>
                            <div>
                                <button>VIEW DISPUTES</button>
                                <button onClick={handleSuccessBoxCancel}>CANCEL</button>
                            </div>
                        </>
                    ) : (
                        <div className={styles.orderUpdatedSuccesfullMain}>
                            <div className={styles.orderUpdatedSuccesBg}>
                                <span className={styles.title}>ORDER UPDATED SUCCESSFULLY</span>
                                <span className={styles.title1}>We have just emailed you the updated order confirmation to <span>{userData?.data?.email_id}</span></span>
                            </div>
                            <span className={styles.orderUpdatedSuccesBg1}>
                                    This order is still actively in the queue for supplier’s to claim for fulfillment. You will be able to chat directly with the supplier on this order once it has been claimed.<br/>
                                    Sit back & relax as your material arrives accurately & on time!
                                </span>
                            <div className={styles.btnSectionOrder}>
                                <button className={styles.btn1}>
                                    <span>Upload Your PO</span>
                                    <span>or, <NAME_EMAIL></span>
                                </button>
                                <button className={styles.btn2}>
                                    <span>DOWNLOAD</span>
                                    <span>CONFIRMATION & W-9</span>
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            )}
            {showConfirmationBox && (  
                <div className={clsx(styles.claimOrderRightWindow,styles.fulfillmentMainWindow)}>
                    {cancelPoNumber ? (
                         <div className={styles.fulfillmentMain}>
                            <span className={styles.fulfillmentNote}>
                                Our records indicate that {watch('fulfilled_by')} has already delivered this order.
                            </span>
                            <span className={styles.fulfillmentNote}>
                                Canceling this order will require a return of material and may incur a restocking fee.
                            </span>
                           <span className={clsx(styles.fulfillmentNote,styles.fulfillmentNote1)}>
                                Upon submission of this cancelation request, {watch('fulfilled_by')} will be notified and must “accept” this change and arrange their material return requirements.
                            </span>
                        </div>

                    ) : (
                        <div className={styles.fulfillmentMain}>
                            <span className={styles.fulfillmentNote}>
                                Your additional material will first be offered to {watch('fulfilled_by')} for fulfillment at ${formatToTwoDecimalPlaces(watch(`cart_items[${watch('cart_items').length - 1}].extended`))}.
                            </span>
                             <span className={styles.fulfillmentNote}>
                                If {watch('fulfilled_by')} is unwilling or unable to fulfill the added line, your material will be presented to the Bryzos supplier network as a standalone item at $150.00.
                            </span>
                             <span className={styles.fulfillmentNote}>
                                Either way, Bryzos is on it. Your order will be fulfilled.
                            </span>
                        </div>
                    )}
                    <div className={styles.btmSection}>
                        <span className={styles.proceedAddLine}>
                            Do you wish to proceed with this {cancelPoNumber ? 'cancelation' : 'added line'}?
                        </span>
                        <button className={styles.btnProceed} onClick={cancelPoNumber ? cancelOrder : saveAddedLine}>
                           <span>PROCEED</span> 
                        </button>
                        <button className={styles.btnCancel} onClick={handleConfirmationBoxCancel}>
                            CANCEL
                        </button>
                    </div>
                </div>
            )}
            {isChatWithVendor && (
                <div className={clsx(styles.claimOrderRightWindow,styles.chatWithVendor)}>
                    <ChatWithVendor />
                </div>
            )}
        </>
    )
}

export default PoHistoryRightWindow