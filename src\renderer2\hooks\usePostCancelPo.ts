import { useGlobalStore } from "@bryzos/giss-ui-library";
import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const usePostCancelPo = () => {

  return useMutation(async (data: any) => {
    try {
      const url = `${import.meta.env.VITE_API_SERVICE}/user/default-order-size`;
      console.log("usePostCancelPo... ", data);
      const response = await axios.post(
        url,
        {data:{
            order_size: "500"
        }}
      );

      if (response.data?.data) {
        return response.data.data;
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default usePostCancelPo;
