import { useEffect, useMemo, useState } from "react";
import styles from "./ViewPoHistoryLeftPanel.module.scss"
import useGetUsersSavedBom from "src/renderer2/hooks/useGetUsersSavedBom";
import Loader from "src/renderer2/Loader/Loader";
import { dateTimeFormat, formatCurrency, formatCurrencyWithComma, formatToTwoDecimalPlaces, getSocketConnection, useCreatePoStore, useGlobalStore } from "@bryzos/giss-ui-library";
import clsx from "clsx";
import { navigatePage } from "src/renderer2/helper";
import { useLeftPanelStore } from "../LeftPanelStore";
import { routes } from "src/renderer2/common";
import dayjs from "dayjs";

const capitalizeText = (text) => {
    if (!text) return '';
    return text.split(' ').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');
};

const ViewPoHistoryLeftPanel = ({viewPoHistoryDataList}: any) => {
    const [selectedSort, setSelectedSort] = useState(3);
    const { setShowLoader , showLoader }: any = useGlobalStore();
    const {setViewPoHistoryData, viewPoHistoryData, bomDataIdToRefresh , setBomDataIdToRefresh } = useCreatePoStore();
    useEffect(() => {
        console.log('>>>>>>>>>>>>>>>>>>>>>>>>>');
        console.log(viewPoHistoryDataList);    
    },[viewPoHistoryDataList])
    
    const sortData = (data: any[], sortBy: number) => {
        const sortedData = [...data];
        switch (sortBy) {
            case 1:
                return sortedData.sort((a, b) => a.buyer_internal_po.localeCompare(b.buyer_internal_po));
            case 2:
                return sortedData.sort((a, b) => b?.buyer_po_price - a?.buyer_po_price);
            case 3:
                return viewPoHistoryDataList;
            default:
                return sortedData;
        }
    };

    const sortedPoHistoryData = useMemo(() => sortData(viewPoHistoryDataList || [], selectedSort), [viewPoHistoryDataList, selectedSort]);

    const handlePoHistoryItemClick = (item: any,index: number) => {
        if(viewPoHistoryData?.buyer_po_number === item?.buyer_po_number || showLoader){
            return;
        }
        if (bomDataIdToRefresh) {
            const socket = getSocketConnection();
            socket?.emit('getBomDataById', { bom_upload_id: bomDataIdToRefresh });
        }
        setShowLoader(true)
        setViewPoHistoryData(null);
        const formattedCreatePoData = {
            delivery_date: dayjs(item?.delivery_date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit),
            shipping_details: {
                line1: item?.line1,
                line2: item?.line2,
                city: item?.city,
                state_code: item?.state_code,
                state_id: Number(item?.state_id),
                zip: item?.zip,
            },
            order_type: item?.type,
            internal_po_number: item?.buyer_internal_po, 
            id: item?.id,
        }           
         setViewPoHistoryData({...formattedCreatePoData, ...item});
        setBomDataIdToRefresh(null)
    }
    return (
        <div className={styles.ViewPoHistoryLeftPanelContainer}>
            <>
                <div className={styles.actionBtns}>
                    <button className={clsx(styles.actionBtn, selectedSort === 1 && styles.active)} onClick={() => setSelectedSort(1)} tabIndex={9}>A to Z</button>
                    <button className={clsx(styles.actionBtn, selectedSort === 2 && styles.active)} onClick={() => setSelectedSort(2)} tabIndex={9}>$  to  $</button>
                    <button className={clsx(styles.actionBtn, selectedSort === 3 && styles.active)} onClick={() => setSelectedSort(3)} tabIndex={9}>New to Old</button>
                </div>
                <div className={styles.savedBomList}>
                    {
                        sortedPoHistoryData?.length > 0 ? sortedPoHistoryData?.map((item: any, index: number) => {
                            const totalPurchase = parseFloat(item.material_total) + parseFloat(item.sales_tax || 0) + parseFloat(item.deposit || 0);
                            return (
                            <span className={clsx(styles.savedBomItem, (viewPoHistoryData?.id === item?.id) && styles.active, (showLoader && !(viewPoHistoryData?.id === item?.id)) && styles.disabled)} key={index} onClick={() => handlePoHistoryItemClick(item, index)} tabIndex={9} onKeyDown={(e) => { if (e.key === 'Enter') { handlePoHistoryItemClick(item, index) } }}>
                                <div className={styles.savedBomTitle}>
                                    <span className={styles.savedBomJobTitle}>{item?.buyer_internal_po ?? ''}</span>
                                    <span className={clsx(styles.savedBomTotalPrice)}>${formatToTwoDecimalPlaces(totalPurchase.toFixed(2))}</span>
                                </div>
                                <div className={styles.savedBomAddress}>
                                    <span className={styles.savedBomPoNumber}>{item?.buyer_po_number || ''}</span>

                                    <div className={styles.savedBomGrid}>
                                        <div className={styles.colLeft}>
                                            <span className={styles.savedBomAddressDetails}><span className={styles.savedBomDetails}>{item?.city ?? ''} {item?.state_code ?? ''} {item?.zip ?? ''}</span></span>
                                            <span className={styles.savedBomDetails}>{item?.delivery_date ? 'Deliver by ' + dayjs(item?.delivery_date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit) : ''}</span>
                                            <span className={styles.savedBomDetails}>
                                                {
                                                    Array.from(new Set(
                                                        item?.items
                                                            // .filter((obj: any) => (obj.status === "APPROVED" || item?.is_draft_po))
                                                            .map((obj: any) => obj?.shape ?? '')
                                                    )).join(', ')
                                                }
                                            </span>
                                        </div>

                                        <span className={styles.savedBomFulfilledBy}>
                                            Fulfilled by:<br/>
                                            {item?.fulfilled_by || 'Not yet claimed'}
                                        </span>
                                    </div>

                                </div>
                            </span>
                        )
                    })
                            :
                            <div className={styles.noDataContainer}>
                                <div className={styles.noData}>
                                    <span>NO PO HISTORY <br /> AVAILABLE YET</span>
                                </div>
                                <button onClick={() => navigatePage(location.pathname, { path: routes.createPoPage })}></button>
                            </div>
                    }
                </div>
            </>
        </div>
    )
}

export default ViewPoHistoryLeftPanel
