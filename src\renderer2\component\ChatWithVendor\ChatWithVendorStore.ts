import { create } from 'zustand';

const defaultStore   = {
    messages: [],
}

interface ChatWithVendorStore {
    channelName: string,
    setChannelName: (channelName: string) => void,
    poNumber: string,
    setPoNumber: (poNumber: string) => void,
    messages: any[],
    setMessages: (messages: any[]) => void,
}

export const useChatWithVendorStore = create<ChatWithVendorStore>((set, get) => ({
    ...defaultStore,
    channelName: '',
    poNumber: '',
    setChannelName: (channelName: string) => set({ channelName }),
    setPoNumber: (poNumber: string) => set({ poNumber }),
    setMessages: (messages: any[]) => set({ messages }),
}));