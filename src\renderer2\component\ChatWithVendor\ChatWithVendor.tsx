import { useState, useEffect, useRef } from 'react'
import styles from './ChatWithVendor.module.scss'
import { ReactComponent as EmojiIcon } from 'src/renderer2/assets/New-images/Share-Pricing/Emoji.svg';
import { ReactComponent as EmojiIconHover } from 'src/renderer2/assets/New-images/Share-Pricing/Emoji-Hover.svg';
import { ReactComponent as BoldIcon } from 'src/renderer2/assets/New-images/Share-Pricing/Bold.svg';
import { ReactComponent as BoldIconHover } from 'src/renderer2/assets/New-images/Share-Pricing/Bold-Hover.svg';
import { ReactComponent as ItalicIcon } from 'src/renderer2/assets/New-images/Share-Pricing/Italic.svg';
import { ReactComponent as ItalicIconHover } from 'src/renderer2/assets/New-images/Share-Pricing/Italic-Hover.svg';
import { ReactComponent as UnderlineIcon } from 'src/renderer2/assets/New-images/Share-Pricing/Underline.svg';
import { ReactComponent as UnderlineIconHover } from 'src/renderer2/assets/New-images/Share-Pricing/Underline-Hover.svg';
import { ReactComponent as AttachmentIcon } from 'src/renderer2/assets/New-images/attachment.svg';
import { ReactComponent as AttachmentHoverIcon } from 'src/renderer2/assets/New-images/attachment-hover.svg';
import { ReactComponent as ExpandIcon } from 'src/renderer2/assets/New-images/Share-Pricing/Expand.svg';
import { ReactComponent as CloseIcon } from 'src/renderer2/assets/New-images/Share-Pricing/Close.svg';
import { useChatWithVendorStore } from './ChatWithVendorStore';
import usePostSendMessage from 'src/renderer2/hooks/usePostSendMessage';
import { v4 as uuidv4 } from 'uuid';

import clsx from 'clsx';
const ChatWithVendor = ({poName}: { poName: string }) => {
    const [showEmojiPicker, setShowEmojiPicker] = useState(false);
    const [activeButtons, setActiveButtons] = useState({
        bold: false,
        italic: false,
        underline: false
    });
    const [lastClickedButton, setLastClickedButton] = useState<string | null>(null);
    const [message, setMessage] = useState('')
    const { channelName, poNumber, messages, setMessages } = useChatWithVendorStore();
    const [wasAtBottom, setWasAtBottom] = useState(true);
    const messageContainerRef = useRef<HTMLDivElement>(null);
    
    const isScrollAtBottom = () => {
        const container = messageContainerRef.current;
        if (!container) return false;
        return (container.scrollHeight - container.scrollTop) - container.clientHeight < 30;
    };
    
    const scrollToBottom = () => {
        const container = messageContainerRef.current;
        if (container) {
            container.scrollTop = container.scrollHeight;
        }
    };
    
    // Handle scroll events to track if we're at the bottom
    const handleScroll = () => {
        setWasAtBottom(isScrollAtBottom());
    };
    
    // When messages change, scroll to bottom if we were at bottom before
    useEffect(() => {
        if (wasAtBottom) {
            setTimeout(() => {
                scrollToBottom();
            }, 100);
        }
    }, [messages]);
    // const [messages, setMessages] = useState([
    //     {
    //         id: 1,
    //         text: "Line 2 was not included on the truck. When will that material be delivered?",
    //         timestamp: "Mar 12, 2025 10:43am",
    //         status:'sent'
    //     }
    // ])

    // Common emojis for the picker
    const commonEmojis = [
        "😀", "😃", "😄", "😁", "😆", "😅", "😂", "🤣", "😊", "😇",
        "🙂", "🙃", "😉", "😌", "😍", "🥰", "😘", "😗", "😙", "😚",
        "😋", "😛", "😝", "😜", "🤪", "🤨", "🧐", "🤓", "😎", "🤩",
        "👍", "👎", "👌", "✌️", "🤞", "🤝", "🙏", "👏", "🎉", "❤️"
    ];

    const [currentMessages, setCurrentMessages] = useState([]);

    const {mutateAsync: sendMessage} = usePostSendMessage(); 

    const handleSendMessage = async () => {
        if (message.trim()) {
            const timeStamp = formatDate(new Date());
            const id = uuidv4();
            setMessages([...messages, { id: id, text: message, formattedTimestamp: timeStamp, status:'sending', isMyMessage: true }]);
            setMessage('')
            console.log(channelName);
            const sendMessageData = await sendMessage({id:id, reference_id: channelName, message: JSON.stringify({text: message, formattedTimestamp: timeStamp }) })
            // if(sendMessageData.status === 'success'){
            //     const mappedMessages = messages.map(message=>{
            //         const {id, text, timestamp, status} = message;
            //         if(id === sendMessageData.id){
            //             return {id, text, timestamp, status:'sent'}
            //         }
            //         return message;
            //     })
            //     setMessages(mappedMessages);
            // }else{
            //     const mappedMessages = messages.map(message=>{
            //         const {id, text, timestamp, status} = message;
            //         if(id === sendMessageData.id){
            //             return {id, text, timestamp, status:'failed'}
            //         }
            //         return message;
            //     })
            //     setMessages(mappedMessages);
            // }
        }
    }

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault()
            handleSendMessage()
        }
    }

    // Close emoji picker when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            const target = event.target as HTMLElement;
            if (showEmojiPicker && !target.closest(`.${styles.emojiPicker}`) && !target.closest(`.${styles.emojiButton}`)) {
                setShowEmojiPicker(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [showEmojiPicker]);
    const handleEmojiButtonClick = () => {
        setShowEmojiPicker(!showEmojiPicker);
        setLastClickedButton('emoji');
    };

    const insertEmoji = (emoji: string) => {
        setShowEmojiPicker(false);
        setMessage(message + emoji);

        console.log(emoji);
    }
    
    function formatDate(date) {
        const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
                        "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        const month = months[date.getMonth()];
        const day = date.getDate();
        const year = date.getFullYear();
        
        let hours = date.getHours();
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const ampm = hours >= 12 ? "pm" : "am";
        hours = hours % 12 || 12;
        
        return `${month} ${day}, ${year} ${hours}:${minutes}${ampm}`;
    }

    return (
            <div className={styles.container}>
                <div className={styles.chatHeader}>
                <div>
                    <div className={styles.poNumberMain}>
                        PO#
                        <span>
                            {poNumber}
                        </span>
                    </div>
                    <div className={styles.vendorName}>
                        Ryerson
                        <span className={styles.vendorRole}>Dakota Belter</span>
                    </div>
                </div>
                  <div className={styles.btnCloseContainer}>
                    <button className={styles.closeButton}>
                        <CloseIcon />
                    </button>
                    <button className={styles.expandButton}>
                        <ExpandIcon />
                    </button>
                  </div>
                </div>
                <div className={styles.chatBody}>
                    <div className={styles.chatMessages}>
                        <div 
                        className={styles.messageContainer} 
                        ref={messageContainerRef}
                        onScroll={handleScroll}
                        >
                            {messages.map((message) => (
                                <div key={message.id} className={clsx(styles.message, message.isMyMessage ? styles.myMessage : styles.othersMessage)}>
                                    <div className={styles.messageTimestamp}>{message.formattedTimestamp}</div>
                                    <div className={styles.messageBubble}>{message.text}</div>
                                </div>
                            ))}
                        </div>
                    </div>
    
                </div>
                <div className={styles.inputSection}>
                    <div className={styles.inputContainer}>
                        <input
                            type="text"
                            value={message}
                            onChange={(e) => setMessage(e.target.value)}
                            onKeyDown={handleKeyDown}
                            placeholder="Type your message..."
                            className={styles.messageInput}
                        />
                    </div>
                    <div>
                        <div className={styles.formattingToolbarContainer}>
                            <div className={styles.formattingToolbar}>
                                <div className={styles.emojiContainer}>
                                    <button
                                        type="button"
                                        className={clsx(styles.formattingButton, showEmojiPicker && styles.emojiButton)}
                                        onClick={handleEmojiButtonClick}
                                        aria-label="Insert emoji"
                                    >
                                        <EmojiIcon className={styles.Icon} />  <EmojiIconHover className={styles.IconHover} />
                                    </button>
                                    {showEmojiPicker && (
                                        <div className={styles.emojiPicker}>
                                            {commonEmojis.map((emoji, index) => (
                                                <button
                                                    key={index}
                                                    type="button"
                                                    className={styles.emojiOption}
                                                    onClick={() => insertEmoji(emoji)}
                                                >
                                                    {emoji}
                                                </button>
                                            ))}
                                        </div>
                                    )}
                                </div>
                                <button
                                    type="button"
                                    className={clsx(styles.formattingButton, activeButtons.bold && styles.buttonClicked)}
                                    onClick={() => handleFormat('bold')}
                                    aria-label="Bold text"
                                >
                                    <BoldIcon className={styles.Icon} />  <BoldIconHover className={styles.IconHover} />
                                </button>
                                <button
                                    type="button"
                                    className={clsx(styles.formattingButton, activeButtons.italic && styles.buttonClicked)}
                                    onClick={() => handleFormat('italic')}
                                    aria-label="Italic text"
                                >
                                    <ItalicIcon className={styles.Icon} />  <ItalicIconHover className={styles.IconHover} />
                                </button>
                                <button
                                    type="button"
                                    className={clsx(styles.formattingButton, activeButtons.underline && styles.buttonClicked)}
                                    onClick={() => handleFormat('underline')}
                                    aria-label="Underline text"
                                >
                                    <UnderlineIcon className={styles.Icon} />  <UnderlineIconHover className={styles.IconHover} />
                                </button>
                                 <button
                                    type="button"
                                    className={clsx(styles.formattingButton, activeButtons.underline && styles.buttonClicked)}
                                  
                                    aria-label="attachment"
                                >
                                    <AttachmentIcon className={clsx(styles.attachmentIcon,styles.Icon)} />  <AttachmentHoverIcon className={clsx(styles.attachmentIcon,styles.IconHover)} />
                                </button>
                            </div>
    
                            <div className={styles.buttonContainer}>
                                <button className={styles.sendButtonDisabled}>send</button>
                            </div>
                        </div>
                    </div>
    
                </div>
    
            </div>
        )
}

export default ChatWithVendor
